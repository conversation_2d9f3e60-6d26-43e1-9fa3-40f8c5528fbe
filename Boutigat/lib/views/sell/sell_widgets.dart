import 'dart:developer';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'condition_page.dart';
import 'brand_page.dart';
import 'category_page.dart';
import 'dart:io';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:shimmer/shimmer.dart';

// Shimmer widgets for loading states
class ShimmerTextInput extends StatelessWidget {
  final double height;
  final EdgeInsets margin;
  final EdgeInsets padding;

  const ShimmerTextInput({
    Key? key,
    this.height = 56.0,
    this.margin = const EdgeInsets.symmetric(vertical: 4),
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      margin: margin,
      child: Container(
        padding: padding,
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label shimmer
              Container(
                width: 80,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              SizedBox(height: 12),
              // Input field shimmer
              Container(
                width: double.infinity,
                height: height,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ShimmerListTile extends StatelessWidget {
  final EdgeInsets margin;
  final EdgeInsets padding;

  const ShimmerListTile({
    Key? key,
    this.margin = const EdgeInsets.symmetric(vertical: 4),
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      margin: margin,
      child: Container(
        padding: padding,
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Row(
            children: [
              // Icon shimmer
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 8),
              // Label shimmer
              Expanded(
                child: Container(
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              SizedBox(width: 16),
              // Value shimmer
              Container(
                width: 100,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              SizedBox(width: 8),
              // Arrow shimmer
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ShimmerTextArea extends StatelessWidget {
  final EdgeInsets margin;
  final EdgeInsets padding;

  const ShimmerTextArea({
    Key? key,
    this.margin = const EdgeInsets.only(bottom: 20),
    this.padding = const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      margin: margin,
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: [
            Container(
              padding: padding,
              child: Column(
                children: [
                  // Title field shimmer
                  Container(
                    width: double.infinity,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  SizedBox(height: 16),
                  // Divider shimmer
                  Container(
                    width: double.infinity,
                    height: 1,
                    color: Colors.grey[300],
                  ),
                  SizedBox(height: 16),
                  // Description field shimmer (multiple lines)
                  ...List.generate(4, (index) => Padding(
                    padding: EdgeInsets.only(bottom: 8),
                    child: Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
class PhotoActionsWidget extends StatelessWidget {
  final PhotoActionsController controller = Get.find<PhotoActionsController>();

  PhotoActionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: <Widget>[
          Padding(
            padding: EdgeInsets.only(bottom: sidePadding, top: sidePadding),
            child: Text(
              "add_up_to_10_photos".tr,
              style: TextStyle(
                fontSize: AppTextSizes.bodyText,
                fontWeight: AppFontWeights.regular,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Obx(() {



            print('photos added: ${controller.photos.value}');
            if (controller.photos.isEmpty) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  buildActionButton("take_photo".tr, FontAwesomeIcons.camera,
                      controller.takePhoto, controller.photoButtonColor,context),
                  buildActionButton("upload_photo".tr,
                      FontAwesomeIcons.arrowUpFromBracket, controller.uploadPhoto, controller.uploadButtonColor,context),
                ],
              );
            } else {
              return Column(
                children: [
                  SizedBox(
                    height: 100,
                    child: ReorderableListView(
                      scrollDirection: Axis.horizontal,
                      proxyDecorator: (child, index, animation) => Material(
                        type: MaterialType.transparency,
                        child: child,
                      ),
                      children: <Widget>[
                        ...controller.photos.map((photoPath) => Container(
                              key: ValueKey(photoPath),
                              margin: const EdgeInsets.symmetric(horizontal: 8.0),
                              child: buildPhotoView(context, photoPath),
                            )).toList(),
                        buildAddMoreButton(key: const ValueKey('add_more_button')),
                      ],
                      onReorder: (oldIndex, newIndex) {
                        if (oldIndex < controller.photos.length && newIndex <= controller.photos.length) {
                          if (newIndex > controller.photos.length) {
                            newIndex = controller.photos.length;
                          }
                          if (oldIndex < newIndex) {
                            newIndex -= 1;
                          }
                          controller.reorderPhotos(oldIndex, newIndex);
                        }
                      },
                    ),
                  ),
                ],
              );
            }
          }),
          SizedBox(height: 5),
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              "hold_and_drag".tr,
              style: TextStyle(
                fontSize: AppTextSizes.bodySmall,
                fontWeight: AppFontWeights.regular,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 5),
        ],
      ),
    );
  }



  Widget buildActionButton(String text, IconData icon, VoidCallback onPressed, Rx<Color> buttonColor,context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 30.0),
      child: SizedBox(
        width: 172.5,
        child: TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.background,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
              side: BorderSide(color: Theme.of(context).disabledColor, width: 1),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Obx(() => Icon(icon, color: buttonColor.value)),
              SizedBox(width: 10),
              Obx(() => Text(text, style: TextStyle(color: buttonColor.value,fontSize: AppTextSizes.bodySmall))),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildPhotoView(BuildContext context, String photoPath) {
    // Check if the path is a network URL or local file path
    final bool isNetworkImage = photoPath.startsWith('http://') || photoPath.startsWith('https://');



 
    print('photoPath: $photoPath');
    print('isNetworkImage: $isNetworkImage');
    final ImageProvider imageProvider = isNetworkImage 
        ? NetworkImage(photoPath)
        : FileImage(File(photoPath)) as ImageProvider;
    
    return GestureDetector(
      onTap: () => openImageSlider(context, photoPath),
      child: Stack(
        alignment: Alignment.topRight,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
            width: 140,
            height: 100,
            child: isNetworkImage 
                ? Image.network(
                     photoPath,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
                      );
                    },
                  )
                : null,
          ),
        ],
      ),
    );
  }

  Widget buildAddMoreButton({required Key key}) {
    return IconButton(
      key: key,
      icon: Icon(Icons.add_circle, color: AppColors.primary, size: 35),
      onPressed: () {
  if (controller.photos.length >= 10) {
    Get.snackbar("Limit reached", "You can only add up to 10 photos.");
    return;
  }

  Get.bottomSheet(
  Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Theme.of(Get.context!).colorScheme.background,
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            buildActionButton(
              "take_photo".tr,
             FontAwesomeIcons.camera,

              () {
                Get.back();
                controller.takePhoto();
              },
              controller.photoButtonColor,
              Get.context!,
            ),
             buildActionButton(
          "upload_photo".tr,
          FontAwesomeIcons.arrowUpFromBracket,
          () {
            Get.back();
            controller.uploadPhoto();
          },
          controller.uploadButtonColor,
          Get.context!,
        ),
          ],
        ),
        const SizedBox(height: 12),
       
      ],
    ),
  ),
);

}

    );
  }

  void openImageSlider(BuildContext context, String initialPhoto) {
    final initialPage = controller.photos.indexOf(initialPhoto);
    int currentPage = controller.photos.indexOf(initialPhoto);

    showDialog(
      context: context,
      builder: (context) {
        double screenWidth = MediaQuery.of(context).size.width;
        double screenHeight = MediaQuery.of(context).size.height;
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.zero,
              child: Container(
                width: screenWidth,
                height: screenHeight * 0.6,
                child: Stack(
                  children: [
                    PageView.builder(
                      controller: PageController(initialPage: initialPage),
                      itemCount: controller.photos.length,
                      onPageChanged: (int page) => setState(() => currentPage = page),
                      itemBuilder: (context, index) {
                        final photoPath = controller.photos[index];
                        final bool isNetworkImage = photoPath.startsWith('http://') || photoPath.startsWith('https://');
                        
                        return isNetworkImage
                            ? Image.network(
                                photoPath,
                                fit: BoxFit.contain,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[300],
                                    child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
                                  );
                                },
                              )
                            : Image.file(
                                File(photoPath),
                                fit: BoxFit.contain,
                              );
                      },
                    ),
                    // Pagination indicators
                    Positioned(
                      bottom: 10,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          controller.photos.length,
                          (index) => Container(
                            margin: EdgeInsets.symmetric(horizontal: 4),
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: currentPage == index ? Colors.white : Colors.white.withOpacity(0.5),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Delete button
                    Positioned(
                      right: 0,
                      top: 0,
                      child: IconButton(
                        icon: Icon(FontAwesomeIcons.deleteLeft, color: Color.fromARGB(255, 255, 92, 71)),
                        onPressed: () {
                          controller.removePhoto(controller.photos[currentPage]);
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class ItemDetailsFormWidget extends StatefulWidget {
  @override
  _ItemDetailsFormWidgetState createState() => _ItemDetailsFormWidgetState();
}

class _ItemDetailsFormWidgetState extends State<ItemDetailsFormWidget> {
  final ItemController itemController = Get.find<ItemController>();
  late TextEditingController priceController;

  @override
  void initState() {
    super.initState();
    priceController = TextEditingController(
      text: itemController.price.value == 0.0 ? '' : itemController.price.value.toString()
    );

    // Listen to changes in the item controller and update the text field
    ever(itemController.price, (double price) {
      if (priceController.text != price.toString() && price != 0.0) {
        priceController.text = price.toString();
      } else if (price == 0.0 && priceController.text.isNotEmpty) {
        // Don't clear the field if user is typing
      }
    });
  }

  @override
  void dispose() {
    priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Obx(() {
          // Check if we're in edit mode (itemId is not null)
          bool isEditMode = Get.find<ItemController>().itemId.value != null;

          // Show shimmer if we're in edit mode and still loading the item
          if (isEditMode && itemController.isLoadingItemForEdit.value) {
            return Column(
              children: [
                ShimmerListTile(),
                ShimmerListTile(),
                ShimmerListTile(),
                SizedBox(height: 16),
                ShimmerTextInput(height: 56),
                SizedBox(height: 8),
              ],
            );
          }
          
          // Check if this is a store item by looking at the selected item
          bool isStoreItem = false;
          if (isEditMode && itemController.selectedItem.value != null) {
            log('store id  ..... ${itemController.selectedItem.value!.storeId}');
            isStoreItem = itemController.selectedItem.value!.storeId != null;
          }
          
          // Allow category editing for store items, disable for regular items in edit mode
          bool shouldShowCategory = !isEditMode || isStoreItem;
          
          return shouldShowCategory
            ? Container(
                color: Theme.of(context).colorScheme.surface,
                margin: EdgeInsets.symmetric(vertical: 4),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        builder: (context) => FractionallySizedBox(
                          heightFactor: 0.95,
                          child: CategoryPage(parentCategory: null),
                        ),
                      );
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                      child: Row(
                        children: [
                          Icon(Icons.category, size: 20, color: AppColors.primary),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'category'.tr,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ),
                          Obx(() => Text(
                            itemController.selectedCategoryTitle.isEmpty ? 'Select category' : itemController.selectedCategoryTitle,
                            style: TextStyle(
                              fontSize: 14,
                              color: itemController.selectedCategoryTitle.isEmpty ? Colors.grey.shade500 : Colors.grey.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          )),
                          SizedBox(width: 8),
                          Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey.shade600),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            : Container();
        }),
        
        // Inline Brand Selection
        Container(
          color: Theme.of(context).colorScheme.surface,
          margin: EdgeInsets.symmetric(vertical: 4),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => Get.to(BrandPage()),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Row(
                  children: [
                    Icon(Icons.branding_watermark, size: 20, color: AppColors.primary),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'brand'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    Obx(() => Text(
                      itemController.brand.value.isEmpty ? 'Select brand' : itemController.brand.value,
                      style: TextStyle(
                        fontSize: 14,
                        color: itemController.brand.value.isEmpty ? Colors.grey.shade500 : Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    )),
                    SizedBox(width: 8),
                    Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey.shade600),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Inline Condition Selection
        Container(
          color: Theme.of(context).colorScheme.surface,
          margin: EdgeInsets.symmetric(vertical: 4),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => Get.to(ConditionPage()),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Row(
                  children: [
                    Icon(Icons.star_rate, size: 20, color: AppColors.primary),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'condition'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    Obx(() => Text(
                      itemController.condition.value.isEmpty ? 'Select condition' : itemController.condition.value,
                      style: TextStyle(
                        fontSize: 14,
                        color: itemController.condition.value.isEmpty ? Colors.grey.shade500 : Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    )),
                    SizedBox(width: 8),
                    Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey.shade600),
                  ],
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 16),
        // Inline Price Input
        Container(
          color: Theme.of(context).colorScheme.surface,
          margin: EdgeInsets.symmetric(vertical: 4),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.attach_money, size: 20, color: AppColors.primary),
                        SizedBox(width: 8),
                        Text(
                          'price'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    TextField(
                      controller: priceController,
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      decoration: InputDecoration(
                        hintText: 'Enter price (UM)',
                        hintStyle: TextStyle(color: Colors.grey.shade500),
                        filled: true,
                        fillColor: Theme.of(context).colorScheme.surface,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.0),
                          borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.0),
                          borderSide: BorderSide(color: AppColors.primary, width: 2.0),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.0),
                          borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        suffixText: 'UM',
                        suffixStyle: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      onChanged: (value) {
                        double? newPrice = double.tryParse(value);
                        if (newPrice != null) {
                          log('new price ... $newPrice');
                          itemController.price.value = newPrice;
                        } else if (value.isEmpty) {
                          itemController.price.value = 0.0;
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8),
      ],
    );
  }


}


class ItemDetailsEntryMainLanguage extends StatelessWidget {
  const ItemDetailsEntryMainLanguage({super.key});

  @override
  Widget build(BuildContext context) {
    final itemController = Get.find<ItemController>();

    return Obx(() {
      // Show shimmer if loading item for edit
      if (itemController.isLoadingItemForEdit.value) {
        return ShimmerTextArea();
      }

      return Container(
        color: Theme.of(context).colorScheme.surface,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: itemController.titleController,
              decoration: InputDecoration(
                hintText: 'title'.tr,
                contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                border: InputBorder.none,
              ),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: AppColors.divider),
            ),
            TextFormField(
              controller: itemController.descriptionController,
              maxLines: 4,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                hintText: 'description'.tr,
                contentPadding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
                border: InputBorder.none,
              ),
              onFieldSubmitted: (_) => FocusScope.of(context).unfocus(),
            ),
          ],
        ),
      );
    });
  }
}
class ItemDetailsEntryArabic extends StatelessWidget {
  const ItemDetailsEntryArabic({super.key});

  @override
  Widget build(BuildContext context) {
    final itemController = Get.find<ItemController>();

    return Obx(() {
      // Show shimmer if loading item for edit
      if (itemController.isLoadingItemForEdit.value) {
        return ShimmerTextArea();
      }

      return Container(
        color: Theme.of(context).colorScheme.surface,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: itemController.titleArController,
              decoration: InputDecoration(
                hintText: 'title_ar'.tr,
                contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                border: InputBorder.none,
              ),
              textDirection: TextDirection.rtl,
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Divider(color: AppColors.divider),
            ),
            TextFormField(
              controller: itemController.descriptionArController,
              maxLines: 4,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                hintText: 'description_ar'.tr,
                contentPadding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
                border: InputBorder.none,
              ),
              textDirection: TextDirection.rtl,
              onFieldSubmitted: (_) => FocusScope.of(context).unfocus(),
            ),
          ],
        ),
      );
    });
  }
}



class CategoryDetailsWidget extends StatefulWidget {
  CategoryDetailsWidget({Key? key}) : super(key: key);

  @override
  _CategoryDetailsWidgetState createState() => _CategoryDetailsWidgetState();
}

class _CategoryDetailsWidgetState extends State<CategoryDetailsWidget> {
  final ItemController controller = Get.find<ItemController>();
  Map<String, TextEditingController> detailControllers = {};

  @override
  void dispose() {
    for (var controller in detailControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  TextEditingController _getControllerForDetail(CategoryDetails detail) {
    final key = detail.id.toString();
    if (!detailControllers.containsKey(key)) {
      detailControllers[key] = TextEditingController(
        text: detail.value?.isNotEmpty == true ? detail.value : ''
      );
    }
    return detailControllers[key]!;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Show shimmer if loading category details
      if (controller.isLoadingCategories.value) {
        return Column(
          children: List.generate(3, (index) => ShimmerTextInput()),
        );
      }

      if (controller.selectedCategoryDetails.isEmpty) {
        return Center(child: Text("No Details Available"));
      }
      return ListView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: controller.selectedCategoryDetails.length,
        itemBuilder: (context, index) {
          final detail = controller.selectedCategoryDetails[index];

          return Container(
            color: Theme.of(context).colorScheme.surface,
            margin: EdgeInsets.symmetric(vertical: 4),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, size: 20, color: AppColors.primary),
                          SizedBox(width: 8),
                          Text(
                            detail.getlabel(),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      TextField(
                        controller: _getControllerForDetail(detail),
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        decoration: InputDecoration(
                          hintText: 'Enter ${detail.getlabel().toLowerCase()}',
                          hintStyle: TextStyle(color: Colors.grey.shade500),
                          filled: true,
                          fillColor: Theme.of(context).colorScheme.surface,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12.0),
                            borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12.0),
                            borderSide: BorderSide(color: AppColors.primary, width: 2.0),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12.0),
                            borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        ),
                        onChanged: (value) {
                          detail.value = value;
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    });
  }

}
