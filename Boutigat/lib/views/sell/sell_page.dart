import 'dart:developer';

import 'package:boutigak/controllers/payment_controller.dart';
import 'package:boutigak/controllers/status_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/widgets/upload_progress_widget.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'sell_widgets.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:boutigak/constants/app_colors.dart';

class SellPage extends StatefulWidget {
  final int? itemId;
  
  const SellPage({Key? key, this.itemId}) : super(key: key);
  
  @override
  _SellPageState createState() => _SellPageState();
}

class _SellPageState extends State<SellPage> {
  final ItemController itemController = Get.find();
  
  @override
  void initState() {
    super.initState();
    _loadItemData();
  }

  @override
  void dispose() {
    itemController.clearItemData();
    photoController.clearPhotoActionData();
    itemController.clearFields();


    super.dispose();
  }
  
  Future<void> _loadItemData() async {
    if (widget.itemId != null) {
      await itemController.loadItemForEdit(widget.itemId!);
    }
  }
  final RxString uploadStatus = "Preparing upload...".obs;
  final int totalSteps = 4;  
  final PhotoActionsController photoController = Get.put(
      PhotoActionsController(Get.find<ItemController>()),
      permanent: true);
  final RxBool isItemUploaded = false.obs;
  final RxDouble uploadProgress = 0.0.obs;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'sell_an_item'.tr,
          style: TextStyle(
              color: Theme.of(context).colorScheme.surface,
              fontSize: AppTextSizes.heading),
        ),
        elevation: 0,
        backgroundColor: AppColors.primary,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            // Show loader if we're in edit mode and still loading the item
            Obx(() {
              if (itemController.isLoadingItemForEdit.value) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.6,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading item...'),
                      ],
                    ),
                  ),
                );
              }
              return SizedBox.shrink();
            }),
            
            // Main content - only show when not loading
            Obx(() {
              if (itemController.isLoadingItemForEdit.value) {
                return SizedBox.shrink();
              }
              
              return Column(
                children: <Widget>[
                  SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        PhotoActionsWidget(),
                        SizedBox(height: 20),
                        ItemDetailsEntryMainLanguage(),
                        SizedBox(height: 20),
                        ItemDetailsEntryArabic(),
                        SizedBox(height: 20),
                        ItemDetailsFormWidget(),
                        Obx(() {
                          if (itemController.selectedCategoryDetails.isEmpty) {
                            return const Center(child: Text(""));
                          }
                          return CategoryDetailsWidget();
                        }),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: EdgeInsets.only(bottom: 30),
                            child: Obx(() {
                              return Column(
                                children: [
                                  if (uploadProgress.value > 0)
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                                      child: UploadProgressWidget(
                                        progress: uploadProgress.value,
                                        status: uploadStatus.value,
                                        isComplete: uploadProgress.value >= 1.0,
                                      ),
                                    ),
                                  SizedBox(height: 10),
                                  CustomButton(
                                    text: widget.itemId != null ? "update_item".tr : "upload_item".tr,
                                    onPressed: () async {
                                      log('in update');
                                      uploadProgress.value = 0.0;
                                      uploadStatus.value = "Preparing upload...";

                                      if (widget.itemId != null) {
                                        await itemController.updateItem(widget.itemId!);
                                        itemController.clearItemData();
                                        photoController.clearPhotoActionData();
                                        itemController.clearFields();

                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text("item_updated_successfully".tr)),
                                        );
                                        await Future.delayed(Duration(seconds: 1));
                                        uploadProgress.value = 0.0;
                                        uploadStatus.value = "";
                                        Get.find<StatusController>().fetchMyItems();
                                        Get.back();
                                      } else {
                                        try {
                                          uploadProgress.value = 0.25;
                                          uploadStatus.value = "Validating data...";
                                          await Future.delayed(Duration(milliseconds: 500));

                                          uploadProgress.value = 0.5;
                                          uploadStatus.value = "Uploading images...";
                                          
                                          uploadProgress.value = 0.75;
                                          uploadStatus.value = "Creating item...";
                                          
                                          int? uploadedItemId = await itemController.postItem((progress) {
                                            uploadProgress.value = 0.75 + (progress * 0.25);
                                          });

                                          uploadProgress.value = 1.0;
                                          uploadStatus.value = "Upload complete!";

                                          if (itemController.isItemUploaded.value) {
                                            Get.to(
                                              PaymentWidget(
                                                amount: itemController.categoryPri.value,
                                                initialAmount: itemController.categoryPri.value,
                                                itemId: uploadedItemId!,
                                              )
                                            );

                                            ScaffoldMessenger.of(context).showSnackBar(
                                              SnackBar(content: Text("item_uploaded_successfully".tr)),
                                            );
                                            
                                            itemController.clearItemData();
                                            photoController.clearPhotoActionData();
                                            itemController.clearFields();
                                            
                                            await Future.delayed(Duration(seconds: 1));
                                            uploadProgress.value = 0.0;
                                            uploadStatus.value = "";
                                          }
                                        } catch (e) {
                                          uploadStatus.value = "Upload failed. Please try again.";
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(content: Text("Error uploading item: ${e.toString()}")),
                                          );
                                        }
                                      }
                                    },
                                  ),
                                ],
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  void showPaymentBottomSheet(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7, 
            child: PaymentForm(onConfirm: () {
              Navigator.pop(context); 
              showConfirmationBottomSheet(context);
            }),
          );
        },
      );
    });
  }

  void showConfirmationBottomSheet(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7, // 70% of the screen height
            child: ConfirmationSheet(),
          );
        },
      );
    });
  }
}

class PaymentForm extends StatefulWidget {
  final VoidCallback onConfirm;

  PaymentForm({required this.onConfirm});

  @override
  State<PaymentForm> createState() => _PaymentFormState();
}

class _PaymentFormState extends State<PaymentForm> {
  final PaymentController paymentController = Get.put(PaymentController());

  InputDecoration inputDecoration(String labelText) {
    return InputDecoration(
      labelText: labelText,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(15),
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    paymentController.fetchPaymentProviders();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16.0,
        right: 16.0,
        top: 24.0,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 120,
            height: 100,
            child: Image.asset(
              "assets/images/bankily.jpg",
              fit: BoxFit.fitHeight,
            ),
          ),
          SizedBox(height: 8.0),
          Obx(() {
            if (paymentController.isLoading.isTrue) {
              return const CircularProgressIndicator();
            } else if (paymentController.isError.isTrue) {
              return const Text(
                  'Erreur lors du chargement des méthodes de paiement');
            } else {
              // Ensure the selectedProvider exists in the list of paymentProviders
              String? selectedProviderValue =
                  paymentController.selectedProvider.value.isEmpty
                      ? null
                      : paymentController.selectedProvider.value;

              // Ensure the list is not empty
              if (paymentController.paymentProviders.isEmpty) {
                selectedProviderValue = null;
              }

              return DropdownButtonFormField<String>(
                value: selectedProviderValue, // Use the corrected value
                onChanged: (newValue) {
                  // Update the selected provider
                  paymentController.selectedProvider.value = newValue ?? '';
                },
                items: paymentController.paymentProviders
                    .map<DropdownMenuItem<String>>((PaymentProvider provider) {
                  return DropdownMenuItem<String>(
                    value: provider.id.toString(), // Ensure uniqueness
                    child: Text(provider.name),
                  );
                }).toList(),
                decoration: inputDecoration('Méthode de paiement'),
              );
            }
          }),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Service:",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    "Abonnement annuel ",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Total à payer (mru)",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                  Text(
                    "1,200 MRU",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 16.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Code B-PAY :',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
              SizedBox(
                width: 10,
              ),
              Text(
                '003271',
                style: TextStyle(fontWeight: FontWeight.normal, fontSize: 18),
              ),
            ],
          ),
          SizedBox(height: 16.0),
          TextField(
            decoration: InputDecoration(
              labelText: 'Entrer le numéro BANKILY',
              border: OutlineInputBorder(),
            ),
          ),
          SizedBox(height: 16.0),
          TextField(
            decoration: InputDecoration(
              labelText: 'Entrer le passcode de paiement',
              border: OutlineInputBorder(),
            ),
            obscureText: true,
          ),
          SizedBox(height: 24.0),
          ElevatedButton(
            onPressed: widget.onConfirm,
            child: Text('Confirmer'),
          ),
        ],
      ),
    );
  }
}

class ConfirmationSheet extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16.0,
        right: 16.0,
        top: 24.0,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 50),
          SizedBox(height: 16.0),
          Text(
            'Paiement Succès',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
          ),
          SizedBox(height: 16.0),
          Text('Transaction ID : 012209271029102241'),
          Text('Montant de paiement : 1,200 MRU'),
          Text('Date de paiement : 29-09-2022 10:29'),
          Text('Service : abonnement annuel Auto'),
          SizedBox(height: 24.0),
          ElevatedButton(
            onPressed: () {
              // Handle PDF download here
              Navigator.pop(context);
            },
            child: Text('Telecharger le reçu PDF'),
          ),
        ],
      ),
    );
  }
}
