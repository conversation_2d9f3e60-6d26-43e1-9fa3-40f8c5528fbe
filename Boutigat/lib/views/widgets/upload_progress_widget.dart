import 'package:flutter/material.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:lottie/lottie.dart';

class UploadProgressWidget extends StatelessWidget {
  final double progress;
  final String status;
  final bool isComplete;

  const UploadProgressWidget({
    Key? key,
    required this.progress,
    required this.status,
    this.isComplete = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      if (isComplete)
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: Lottie.asset(
                            'assets/lottie/Done.json',
                            width: 24,
                            height: 24,
                            repeat: false,
                          ),
                        )
                      else
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                          ),
                        ),
                      const SizedBox(width: 12),
                      Text(
                        isComplete ? 'Upload Complete!' : 'Uploading item...',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: isComplete ? Colors.green : null,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    '${(progress * 100).toInt()}%',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isComplete ? Colors.green : AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Stack(
                children: [
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: 8,
                    width: MediaQuery.of(context).size.width * progress,
                    decoration: BoxDecoration(
                      color: isComplete ? Colors.green : AppColors.primary,
                      borderRadius: BorderRadius.circular(10),
                      gradient: LinearGradient(
                        colors: [
                          (isComplete ? Colors.green : AppColors.primary).withValues(alpha: 0.8),
                          isComplete ? Colors.green : AppColors.primary,
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                status,
                style: TextStyle(
                  color: isComplete ? Colors.green[600] : Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}