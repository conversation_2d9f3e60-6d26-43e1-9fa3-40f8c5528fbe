import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/badge_controller.dart';

import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/profil/boutigak/creation_store_page.dart';
import 'package:boutigak/views/profil/boutigak/edit_store_page.dart';

import 'package:boutigak/views/profil/boutigak/payment_provider_page.dart';
import 'package:boutigak/views/profil/boutigak/mystore_page.dart';
import 'package:boutigak/views/profil/store_order_validation_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';

import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/text_row_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:geolocator/geolocator.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'package:google_maps_flutter/google_maps_flutter.dart' as google_maps;
import 'package:google_maps_flutter/google_maps_flutter.dart';






class BoutigakUserPage extends StatefulWidget {
  @override
  State<BoutigakUserPage> createState() => _BoutigakUserPageState();
}

class _BoutigakUserPageState extends State<BoutigakUserPage> {
  final OrderController orderController = Get.find<OrderController>();

  final TextEditingController fromDateController = TextEditingController();

  final TextEditingController toDateController = TextEditingController();



   final AuthController authController = Get.find<AuthController>();


  
  initState() {
    super.initState();
    

    authController.getAndSaveUser();
    
  }

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find<AuthController>();
    final StoreController storeController = Get.put(StoreController());

    if (authController.user?.hasStore == true) {
      storeController.fetchedMyStoreInformation();
    }
 final String storeName = storeController.myStore.value?.name ?? "My Store";


    return Scaffold(
      appBar: AppBar(title:  Text(storeName)),
      body: Obx(() {
        final bool isSubscribed = authController.hasSubscription.value;
        final bool hasStore = authController.user?.hasStore ?? false;
        final Store? myStore = storeController.myStore.value;

        if (!hasStore && isSubscribed) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (Get.currentRoute != '/store-informations') {
              Get.to(() => StoreInformations());
            }
          });
          return SizedBox();
        }

        return Column(
          children: [
            if (hasStore && myStore != null) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                      
                        CachedImageWidget(
                  imageUrl: '${myStore.images.first}',
                  width:120,
                  height:120,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(8),
                ),
                        Column(
                          children: [
                            Row(
                              children: [
                                TextButton(
                                  onPressed: () {},
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.black,
                                    foregroundColor: AppColors.surface,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: Text(
                                      "${myStore.followersCount} "+"followers".tr),
                                ),
                                SizedBox(width: 10),
                                TextButton(
                                  onPressed: () => Get.to(() => EditStorePage()),
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: AppColors.surface,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: Text("edit".tr),
                                ),
                              ],
                            ),
                            SizedBox(height: 10),
                            Row(
                              children: [
                                TextButton.icon(
                                  onPressed: () {},
                                  style: TextButton.styleFrom(
                                    backgroundColor: AppColors.surface,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      side: BorderSide(
                                          color: Colors.grey.shade300),
                                    ),
                                    foregroundColor: AppColors.onSurface,
                                  ),
                                  icon: Icon(Icons.access_time, size: 16),
                                  label: Text(
                                      "${myStore.openingTime} - ${myStore.closingTime}"),
                                ),
                                SizedBox(width: 12),
                                Obx(() {
                                  final isOpen =
                                      storeController.isStoreOpen.value;
                                  return TextButton.icon(
                                    onPressed: () => storeController
                                        .toggleStoreStatus(!isOpen),
                                    icon: Icon(
                                      isOpen
                                          ? Icons.store
                                          : Icons.store_mall_directory_outlined,
                                      size: 18,
                                      color: isOpen
                                          ? Colors.orange
                                          : Colors.green,
                                    ),
                                    label: Text(
                                      isOpen ? "close_store".tr : "open_store".tr,
                                      style: TextStyle(
                                        color: isOpen
                                            ? Colors.orange
                                            : Colors.green,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    style: TextButton.styleFrom(
                                      backgroundColor: AppColors.surface,
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        side: BorderSide(
                                            color: (isOpen
                                                    ? Colors.orange
                                                    : Colors.green)
                                                .withOpacity(0.5)),
                                      ),
                                    ),
                                  );
                                }),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                
                    const SizedBox(height: 10),
                    SizedBox(
  height: 200, // ≈5 lignes selon le style
  child: Scrollbar(
    thumbVisibility: true,
    child: SingleChildScrollView(
      child: Text(
        myStore.description,
        style: TextStyle(fontSize: 16, color: Colors.grey[700]),
      ),
    ),
  ),
),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    customListTile('list_of_products'.tr, Icons.list,
                        StoreProductPage()),
                    Obx(() {
                      final badgeController = Get.find<BadgeController>();
                      return customListTileWithBadge(
                        'store_orders'.tr,
                        Icons.shopping_cart,
                        () => MyStoreOrdersPage(),
                        badgeController.getModuleCount('store-order'),
                      );
                    }),
                    customListTile('payment_method'.tr, Icons.payment,
                        PaymentProvidersPage()),
                    customListTile('store_location'.tr, Icons.location_pin,
                        StoreLocationManagementPage()),
                    customListTile('subscription'.tr, Icons.subscriptions,
                        StoreSubscriptionPage()),
                  ],
                ),
              ),
            ] else if (!isSubscribed) ...[
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                 'subscription_required'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red),
                ),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () {
                  // Get.to(() => ContactUsPage());
                },
                child:  Text("contact-us".tr),
              ),
            ],
          ],
        );
      }),
    );
  }

  Widget customListTile(String title, IconData icon, Widget page) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 2,
            offset: Offset(0.2, 0.2))
      ]),
      child: Material(
        color: AppColors.surface,
        child: InkWell(
          onTap: () => Get.to(page),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            child: Row(
              children: [
                FaIcon(icon, size: 20.sp, color: AppColors.primary),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(title,
                      style: TextStyle(fontSize: 14.sp, color: Colors.black)),
                ),
                Icon(Icons.arrow_forward_ios,
                    size: 16.sp, color: Colors.grey),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget customListTileWithBadge(
      String title,
      IconData icon,
      Widget Function() pageBuilder,
      int badgeCount) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 2,
            offset: Offset(0.2, 0.2))
      ]),
      child: Material(
        color: AppColors.surface,
        child: InkWell(
          onTap: () {
            Get.to(pageBuilder());
            if (title == 'Orders') {
              final BadgeController badgeController =
                  Get.find<BadgeController>();
              badgeController.resetBadge('store-order');
            }
          },
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            child: Row(
              children: [
                FaIcon(icon, size: 20.sp, color: AppColors.primary),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(title,
                      style: TextStyle(fontSize: 14.sp, color: Colors.black)),
                ),
                AnimatedSwitcher(
                  duration: Duration(milliseconds: 300),
                  transitionBuilder: (child, animation) =>
                      ScaleTransition(scale: animation, child: child),
                  child: badgeCount > 0
                      ? Badge(
                          key: ValueKey<int>(badgeCount),
                          backgroundColor: Colors.red.shade100,
                          offset: Offset(8, 0),
                          label: Text(
                            badgeCount > 9 ? '9+' : '$badgeCount',
                            style: const TextStyle(
                                color: Colors.red,
                                fontSize: 10,
                                fontWeight: FontWeight.bold),
                          ),
                          child: SizedBox(width: 20, height: 20),
                        )
                      : const SizedBox(
                          key: ValueKey<int>(0), width: 20, height: 20),
                ),
                SizedBox(width: 8.w),
                Icon(Icons.arrow_forward_ios,
                    size: 16.sp, color: Colors.grey),
              ],
            ),
          ),
        ),
      ),
    );
  }
}


class MyStoreitemsListViewWidget extends StatelessWidget {
  final Category? category;
  final List<Item> items;
  final String storeImage;

  MyStoreitemsListViewWidget({
    Key? key,
    this.category,
    required this.items,
    required this.storeImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final StoreController storeController = Get.find<StoreController>();

    // print('item id: ${items[0].id}');
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Obx(() {
      // Show loader when items are being fetched
      if (storeController.isLoadingItems.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading items...'),
            ],
          ),
        );
      }

      // Show "no items" message only after loading is complete
      if (items.isEmpty) {
        return Center(
          child: Text("No items available"),
        );
      }

      return _buildItemsList(context, screenWidth, sidePadding);
    });
  }

  Widget _buildItemsList(BuildContext context, double screenWidth, double sidePadding) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                "${items.length} Products",
                style: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.normal),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(
              left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 560,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return _buildItemWidget(context, items[index], screenWidth);
              },
              childCount: items.length,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemWidget(BuildContext context, Item item, double screenWidth) {
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              _showModalBottomSheet(context, item, storeImage);
            },
            child: Stack(
              children: [
                Container(
                  width: screenWidth * 0.4388,
                  height: screenWidth * 0.5485,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: FadeInImage(
                      placeholder: AssetImage(
                          'assets/images/placeholder_logo.png'), // Transparent image as a placeholder
                      image: item.images.isNotEmpty
                          ? NetworkImage(
                              '${item.images.first}')
                          : AssetImage('assets/images/placeholder_logo.png')
                              as ImageProvider,
                      fit: BoxFit.cover,
                      placeholderFit:
                          BoxFit.cover, // Adjust to fit the container
                      fadeInDuration: Duration(
                          milliseconds: 300), // Duration for fade-in effect
                      imageErrorBuilder: (context, error, stackTrace) {
                        // Show a gray background with the centered logo if the image fails to load
                        return Container(
                          color: Colors.grey, // Gray background
                          alignment: Alignment.center,
                          child: Image.asset(
                            'assets/images/placeholder_logo.png',
                            // Logo height
                          ),
                        );
                      },
                    ),
                  ),
                ),
                if (item.hasPromotion ?? false)
                  Positioned(
                    top: 15,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(5),
                          bottomRight: Radius.circular(5),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.yellow,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.discount,
                                color: Theme.of(context).colorScheme.onSurface,
                                size: 15),
                          ),
                          SizedBox(width: 4),
                          Text(
                            "${item.promotionPercentage!.toStringAsFixed(0)}% Discount",
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 10,
                              fontWeight: AppFontWeights.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: screenWidth * 0.0407),
          Text(
            item.title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Row(
              mainAxisSize: MainAxisSize.min, // Évite de prendre tout l’espace
              children: [
                if (item.hasPromotion) ...[
                  Text(
                    '${(item.price / (1 - item.promotionPercentage! / 100)).toStringAsFixed(0)} mru',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: const Color.fromARGB(
                          255, 131, 131, 131), // Prix avant remise en gris
                      decoration: TextDecoration.lineThrough, // Barré
                    ),
                  ),
                  SizedBox(width: 5), // Espacement entre les prix
                ],
                Text(
                  '${item.price.toStringAsFixed(0)} mru',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary, // Prix après réduction
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addToOrder(Item item, int quantity, BuildContext context) {
    // Initialize OrderController with lazyPut to ensure it's only created when needed
    final OrderController orderController =
        Get.put(OrderController(initialOrderId: 1));
    final ItemController itemController = Get.put(ItemController());

    // Find if item exists in the order
    var existingOrderItem = orderController.items.firstWhereOrNull(
      (orderItem) => orderItem.itemId.value == item.id,
    );

    // If item exists, increment quantity
    if (existingOrderItem != null) {
      existingOrderItem.incrementQuantity();
    } else {
      // Add new item to order
      var newItem = OrderItemController(
        initialItemId: item.id!,
        initialQuantity: quantity,
      );
      orderController.addItem(newItem);
    }

    // Recalculate the total
    orderController.calculateTotal();

    Navigator.pop(context);
    orderController.update();
  }

  void _showModalBottomSheet(BuildContext context, Item item, String storeImage) {
  PageController pageController = PageController();
  int currentPage = 0;

  int quantity = 1;
  OrderController orderController = Get.put(OrderController(initialOrderId: 1));
  ItemController itemController = Get.put(ItemController());
  TextEditingController promotionController = TextEditingController();

  showModalBottomSheet(
    backgroundColor: Theme.of(context).colorScheme.surface,
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return Container(
            height: MediaQuery.of(context).size.height,
            child: Stack(
              children: [
                Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          ImageSlider(
                            pageController: pageController,
                            photos: item.images.map((image) => '$image').toList(),
                            currentPage: currentPage,
                            onPageChanged: (int page) => setState(() => currentPage = page),
                            borderRadius: 0,
                          ),
                          Positioned(
                            top: 60,
                            right: 10,
                            child: ClipOval(
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                child: Container(
                                  color: AppColors.onBackground.withOpacity(0.2),
                                  child: IconButton(
                                    iconSize: 20,
                                    icon: const Icon(FontAwesomeIcons.upRightFromSquare, color: AppColors.background),
                                    onPressed: () => print('Share Icon Tapped!'),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 60,
                            left: 10,
                            child: ClipOval(
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                child: Container(
                                  color: AppColors.onBackground.withOpacity(0.3),
                                  child: IconButton(
                                    iconSize: 20,
                                    icon: const Icon(FontAwesomeIcons.chevronDown, color: AppColors.background),
                                    onPressed: () => Navigator.pop(context),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(child: Container()),
                  ],
                ),
                Positioned(
                  top: MediaQuery.of(context).size.height * 0.49,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
                    ),
                    child: Column(
                      children: [
                        MyStoreInfoSectionWidget(
                          boutiquePhotoUrl: storeImage,
                          item: item,
                          orderController: orderController,
                          
                          dominantColor: Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 30.0, left: 25, right: 25),
                      child: Column(
                        children: [
                          SizedBox(height: 20),
                          Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Price (mru)",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Theme.of(context).disabledColor,
                                    ),
                                  ),
                                  Text(
                                    "${(item.price * quantity).toStringAsFixed(0)} ",
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                              Spacer(),
                              Row(
                                children: [
                                  Container(
                                    width: 80,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.onSurface,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          spreadRadius: 2,
                                          blurRadius: 5,
                                          offset: const Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: TextButton(
                                      onPressed: () async {
                                        Get.to(() => SellStorePage(itemId: item.id));
                                      },
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(30),
                                        ),
                                      ),
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: const Text(
                                          "Edit",
                                          style: TextStyle(
                                            color: AppColors.surface,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 10),
                               Container(
  width: 120,
  height: 50,
  decoration: BoxDecoration(
    color: Theme.of(context).colorScheme.onSurface,
    borderRadius: BorderRadius.circular(20),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.2),
        spreadRadius: 2,
        blurRadius: 5,
        offset: const Offset(0, 3),
      ),
    ],
  ),
  child: TextButton(
    onPressed: () async {
      _showPromotionDialog(context, item);
    },
    style: TextButton.styleFrom(
      padding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
      ),
    ),
    child: Container(
      alignment: Alignment.center,
      child: Text(
        item.hasPromotion
            ? "Promo: ${item.promotionPercentage}%" // Si promotion active, affiche le pourcentage
            : "Set Promotion", // Sinon affiche "Set Promotion"
        style: const TextStyle(
          color: AppColors.surface,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    ),
  ),
),
SizedBox(width: 10),
Container(
  width: 50,
  height: 50,
  decoration: BoxDecoration(
    color: AppColors.error,
    borderRadius: BorderRadius.circular(20),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.2),
        spreadRadius: 2,
        blurRadius: 5,
        offset: Offset(0, 3),
      ),
    ],
  ),
  child: TextButton(
    onPressed: () async {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text("Confirm Deletion"),
            content: const Text("Are you sure you want to delete this item?"),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text("Cancel"),
              ),
              ElevatedButton(
                onPressed: () {
                  itemController.deleteStoreItem(item.id!);
                  Navigator.of(context).pop(); // Ferme le dialog
                  Navigator.of(context).pop(); // Ferme le bottom sheet
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
                child: const Text(
                  "Delete",
                  style: TextStyle(color: AppColors.surface),
                ),
              ),
            ],
          );
        },
      );
    },
    style: TextButton.styleFrom(
      padding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
      ),
    ),
    child: Container(
      alignment: Alignment.center,
      child: const Icon(
        Icons.delete,
        color: AppColors.surface,
        size: 24,
      ),
    ),
  ),
),

                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}


// 🎯 **Fonction qui affiche la boîte de dialogue pour entrer le pourcentage de promotion**
  void _showPromotionDialog(BuildContext context, Item item) {
    TextEditingController promotionController = TextEditingController();
    final StoreController storeController = Get.put(StoreController());
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("Set Promotion"),
          content: TextField(
            controller: promotionController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(hintText: "Enter discount percentage"),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Cancel"),
            ),
            TextButton(
              onPressed: () async {
                double? discount = double.tryParse(promotionController.text);
                if (discount != null && discount > 0) {
                  await storeController.setPromotionForItem(item.id!, discount);
                  Navigator.pop(context); // Fermer le dialogue après validation
                } else {
                  // Get.snakbar("Error", "Please enter a valid discount percentage");
                }
              },
              child: Text("Apply"),
            ),
            if (item.hasPromotion)
              Container(
                width: 140,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.redAccent,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextButton(
                  onPressed: () async {
                    await storeController.removePromotionForItem(item.id!);
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    child: const Text(
                      "Remove Promotion",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class MyStoreInfoSectionWidget extends StatefulWidget {
  final String boutiquePhotoUrl;
  final Item item;
  final OrderController orderController;

  final Color? dominantColor;

  MyStoreInfoSectionWidget({
    Key? key,
    required this.boutiquePhotoUrl,
    required this.item,
    required this.orderController,
    this.dominantColor,
  }) : super(key: key);

  @override
  _MyStoreInfoSectionWidgetState createState() =>
      _MyStoreInfoSectionWidgetState();
}

class _MyStoreInfoSectionWidgetState extends State<MyStoreInfoSectionWidget> {
  var isDescriptionSelected = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 50.0,
                height: 50.0,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    image: DecorationImage(
                      image: NetworkImage(widget.boutiquePhotoUrl),
                      fit: BoxFit.cover,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: Offset(0.5, 0.5), // Position de l'ombre
                      ),
                    ]),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  widget.item.title,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      isDescriptionSelected = true;
                    });
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size(0, 0),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: isDescriptionSelected
                              ? AppColors.onBackground
                              : Colors.transparent,
                          width: 2.0,
                        ),
                      ),
                    ),
                    child: Text(
                      "Description",
                      style: TextStyle(
                        color: isDescriptionSelected
                            ? AppColors.onBackground
                            : Colors.grey,
                      ),
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      isDescriptionSelected = false;
                    });
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size(0, 0),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: isDescriptionSelected
                              ? Colors.transparent
                              : AppColors.onBackground,
                          width: 2.0,
                        ),
                      ),
                    ),
                    child: Text(
                      "Details",
                      style: TextStyle(
                        color: isDescriptionSelected
                            ? Colors.grey
                            : AppColors.onBackground,
                      ),
                    ),
                  ),
                ),
                Spacer(),
                Text(
                  widget.item.createdAt != null
                      ? "${timeago.format(widget.item.createdAt!)} "
                      : "Date unknown",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
          isDescriptionSelected
              ? Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(widget.item.description,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.medium,
                      )),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 5),
                    TextRow(
                      label: "Condition: ",
                      value: widget.item.condition,
                    ),
                    TextRow(
                      label: "Category: ",
                      value: widget.item.categoryName ?? "Unknown",
                    ),
                    TextRow(
                      label: "Brand: ",
                      value: widget.item.brandName ?? "Unknown",
                    ),
                  ],
                ),
        ],
      ),
    );
  }
}

class InfoSectionWidget extends StatefulWidget {
  final String boutiquePhotoUrl;
  final Item item;
 // final OrderController orderController;
  final bool isFavorited;
  final Color? dominantColor;
  final void Function()? toggleFavorite;

  InfoSectionWidget({
    Key? key,
    required this.boutiquePhotoUrl,
    required this.item,
  //  required this.orderController,
    required this.isFavorited,
    required this.toggleFavorite,
    this.dominantColor,
  }) : super(key: key);

  @override
  _InfoSectionWidgetState createState() => _InfoSectionWidgetState();
}

class _InfoSectionWidgetState extends State<InfoSectionWidget> {
  var isDescriptionSelected = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 50.0,
                height: 50.0,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    image: DecorationImage(
                      image: NetworkImage(widget.boutiquePhotoUrl),
                      fit: BoxFit.cover,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: Offset(0.5, 0.5), // Position de l'ombre
                      ),
                    ]),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  widget.item.title,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              IconButton(
                icon: Icon(
                  widget.isFavorited ? Icons.favorite : Icons.favorite_border,
                  size: 30,
                  color: Colors.red,
                ),
                onPressed: widget.toggleFavorite,
              ),
            ],
          ),
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      isDescriptionSelected = true;
                    });
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size(0, 0),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: isDescriptionSelected
                              ? AppColors.onBackground
                              : Colors.transparent,
                          width: 2.0,
                        ),
                      ),
                    ),
                    child: Text(
                      "Description",
                      style: TextStyle(
                        color: isDescriptionSelected
                            ? AppColors.onBackground
                            : Colors.grey,
                      ),
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      isDescriptionSelected = false;
                    });
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size(0, 0),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: isDescriptionSelected
                              ? Colors.transparent
                              : AppColors.onBackground,
                          width: 2.0,
                        ),
                      ),
                    ),
                    child: Text(
                      "Details",
                      style: TextStyle(
                        color: isDescriptionSelected
                            ? Colors.grey
                            : AppColors.onBackground,
                      ),
                    ),
                  ),
                ),
                Spacer(),
                Text(
                  widget.item.createdAt != null
                      ? "${timeago.format(widget.item.createdAt!)} "
                      : "Date unknown",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
          isDescriptionSelected
              ? Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(widget.item.description,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.medium,
                      )),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 5),
                    TextRow(
                      label: "Condition: ",
                      value: widget.item.condition,
                    ),
                    TextRow(
                      label: "Category: ",
                      value: widget.item.categoryName ?? "Unknown",
                    ),
                    TextRow(
                      label: "Brand: ",
                      value: widget.item.brandName ?? "Unknown",
                    ),
                  ],
                ),
        ],
      ),
    );
  }
}/* ───────────── Statut → Couleur ───────────── */
Color _statusColor(String? status) {
  switch (status) {
    case 'PENDING':
      return Colors.grey;
    case 'ACCEPTED':
      return Colors.blue;
    case 'CANCELLED':
      return Colors.red;
    case 'WAITING_PAYMENT':
      return Colors.deepOrange;
    case 'DELIVERED':
    case 'COMPLETED':
      return Colors.green;
    default:
      return Colors.black26;
  }
}

class StoreOrderDetailsPage extends StatelessWidget {
  final Order order;
  StoreOrderDetailsPage({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final orderCtl = Get.find<OrderController>();
    final delivery =
        double.tryParse(order.deliveryCharge ?? '0') ?? 0.0;
    final total = (order.totalOrders ?? 0).toDouble() + delivery;



    print( ' order in details page ${order.toJson()}');
    return Scaffold(
      appBar: AppBar(title: Text('Order #${order.orderId}')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          /* ---- En-tête ---- */
          Row(
            children: [
              const Icon(Icons.store, size: 40, color: Colors.blue),
              const SizedBox(width: 12),
              Expanded(
                  child: Text('Order #${order.orderId}',
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.w600))),
              Chip(
                label: Text(order.status ?? '',
                    style: const TextStyle(color: Colors.white)),
                backgroundColor: _statusColor(order.status),
              ),
            ],
          ),
          const SizedBox(height: 24),

          /* ---- Customer Information ---- */
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Customer Information',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.person, size: 20, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text('${order.userFirstName ?? ''} ${order.userLastName ?? ''}',
                        style: const TextStyle(fontSize: 14)),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.phone, size: 20, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(order.userPhone ?? 'No phone number',
                        style: const TextStyle(fontSize: 14)),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          /* ---- Items ---- */
          ...order.items.map((it) {
            final tag = it.itemId.toString();
            final itemCtl = Get.put(ItemController(), tag: tag)
              ..fetchItemById(it.itemId);

            return Obx(() {
              final item = itemCtl.selectedItem.value;
              if (item == null) {
                return const ListTile(
                  title: Text('Loading item…'),
                  subtitle: LinearProgressIndicator(),
                );
              }
              final lineTotal = item.price * it.quantity;
              return ListTile(
                contentPadding: EdgeInsets.zero,
                leading: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: item.images.isNotEmpty
                      ? Image.network(
                          '${item.images.first}',
                          width: 48,
                          height: 48,
                          fit: BoxFit.cover,
                        )
                      : Image.asset('assets/default_image.png',
                          width: 48, height: 48),
                ),
                title:
                    Text(item.title, style: const TextStyle(fontWeight: FontWeight.w600)),
                subtitle: Text('${it.quantity} × ${item.price.toStringAsFixed(2)} UM'),
                trailing: Text('${lineTotal.toStringAsFixed(2)} UM',
                    style: const TextStyle(fontWeight: FontWeight.bold)),
              );
            });
          }),

          const Divider(height: 32),
          _kv('Items Subtotal', '${(order.totalOrders ?? 0).toStringAsFixed(2)} UM'),
          const SizedBox(height: 8),
          _kv('Delivery Charge', '${delivery.toStringAsFixed(2)} UM'),
          const SizedBox(height: 8),
          _kv('Total', '${total.toStringAsFixed(2)} UM',
              bold: true, color: Colors.green),

          if (order.isPaid) _paidSection(context),

          if (!order.isPaid &&
              order.status != 'DELIVERED' &&
              order.status != 'CANCELLED') ...[
            const SizedBox(height: 24),
            Row(mainAxisAlignment: MainAxisAlignment.end, children: [
              ElevatedButton(
                onPressed: () => Get.to(
                    () => StoreOrderValidationPage(order: order)),
                child: const Text('Validate'),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                onPressed: () {
                  orderCtl.changeOrderStatus(order.orderId!, 'CANCELLED');
                  Get.back();
                },
                child: const Text('Cancel'),
              ),
            ]),
          ],
        ]),
      ),
    );
  }

  /* ---- Key/Value Row ---- */
  Widget _kv(String k, String v, {bool bold = false, Color? color}) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(k),
          Text(v,
              style: TextStyle(
                  fontWeight: bold ? FontWeight.bold : FontWeight.normal,
                  color: color)),
        ],
      );

  /* ---- Sections annexes ---- */
  Widget _paidSection(BuildContext ctx) => Column(children: [
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(mainAxisSize: MainAxisSize.min, children: const [
            Icon(Icons.check_circle, color: Colors.green, size: 20),
            SizedBox(width: 8),
            Text('Payment Received',
                style: TextStyle(
                    color: Colors.green, fontWeight: FontWeight.bold)),
          ]),
        ),
        const SizedBox(height: 16),
        Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
          ElevatedButton.icon(
            icon: const Icon(Icons.receipt_long),
            label: const Text('View Payment'),
            onPressed: () => _showPaymentScreenshot(ctx),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.location_on),
            label: const Text('View Location'),
            onPressed: () => _showDeliveryLocation(ctx),
          ),
        ]),
      ]);

  /* ---- Dialogs screenshot / map ---- */
  void _showPaymentScreenshot(BuildContext ctx) {


    print('order paymanet screenshot ${order.paymentScreenshot}');
    if (order.paymentScreenshot == null) return;
    showDialog(
      context: ctx,
      builder: (_) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            const Text('Payment Screenshot',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            const SizedBox(height: 16),
            Image.network(
              '${order.paymentScreenshot}', height: 300, fit: BoxFit.contain),
            const SizedBox(height: 16),
            ElevatedButton(
                onPressed: () => Navigator.pop(ctx), child: const Text('Close'))
          ]),
        ),
      ),
    );
  }

  void _showDeliveryLocation(BuildContext ctx) {
    if (order.deliveryLatitude == null || order.deliveryLongitude == null) return;
    showDialog(
      context: ctx,
      builder: (_) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: SizedBox(
          height: 400,
          child: Column(children: [
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text('Delivery Location',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            ),
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: gmaps.GoogleMap(
                  initialCameraPosition: gmaps.CameraPosition(
                    target: gmaps.LatLng(
                        order.deliveryLatitude!, order.deliveryLongitude!),
                    zoom: 15,
                  ),
                  markers: {
                    gmaps.Marker(
                      markerId: const gmaps.MarkerId('delivery'),
                      position: gmaps.LatLng(
                          order.deliveryLatitude!, order.deliveryLongitude!),
                      infoWindow: gmaps.InfoWindow(
                          title: 'Delivery Location',
                          snippet: order.deliveryAddress ?? ''),
                    ),
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
                onPressed: () => Navigator.pop(ctx), child: const Text('Close')),
            const SizedBox(height: 8),
          ]),
        ),
      ),
    );
  }
}
class MyStoreOrdersPage extends StatelessWidget {
  final OrderController orderController = Get.put(OrderController());
  final _fromDateCtrl = TextEditingController();
  final _toDateCtrl   = TextEditingController();

  MyStoreOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    orderController.fetchMyStoreOrders();              // appel initial

    return Scaffold(
     
 appBar: AppBar(
        title: Text(
          'store_orders'.tr,
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 18,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Theme.of(context).colorScheme.primary),
          actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: Obx(() {
        if (orderController.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        if (orderController.myStoreOrders.isEmpty) {
          return const Center(child: Text('No store orders found.'));
        }

        return ListView.builder(
          itemCount: orderController.myStoreOrders.length,
          itemBuilder: (context, i) {
            final order = orderController.myStoreOrders[i];
            final delivery =
                double.tryParse(order.deliveryCharge ?? '0') ?? 0;
            final total = (order.totalOrders ?? 0).toDouble() + delivery;

            return GestureDetector(
  onTap: () {
    Get.to(() => StoreOrderDetailsPage(order: order),
        transition: Transition.rightToLeft);
  },
  child: Container(
    margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
    padding: const EdgeInsets.all(8.0),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16.0),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.3),
          spreadRadius: 1,
          blurRadius: 4,
          offset: const Offset(0.4, 0.4),
        ),
      ],
    ),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            color: _statusColor(order.status).withOpacity(0.15),
            shape: BoxShape.circle,
          ),
          child: Icon(Icons.receipt_long, color: _statusColor(order.status)),
        ),
        const SizedBox(width: 12.0),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Order #${order.orderId}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _statusColor(order.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  order.status ?? 'Unknown',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: _statusColor(order.status),
                  ),
                ),
              ),
            ],
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${total.toStringAsFixed(2)} UM',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              order.isPaid ? 'Paid' : 'Unpaid',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: order.isPaid ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
      ],
    ),
  ),
);

          },
        );
      }),
    );
  }

  /* ───────────── Dialog filtre ───────────── */
  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Filter Orders'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _dateField(ctx, 'From', _fromDateCtrl),
            const SizedBox(height: 16),
            _dateField(ctx, 'To', _toDateCtrl),
            const SizedBox(height: 16),
            Obx(() => DropdownButtonFormField<String>(
                  value: orderController.paymentStatusFilter.value,
                  items: const [
                    DropdownMenuItem(value: '', child: Text('All')),
                    DropdownMenuItem(value: 'paid', child: Text('Paid')),
                    DropdownMenuItem(value: 'unpaid', child: Text('Unpaid')),
                  ],
                  onChanged: (val) =>
                      orderController.paymentStatusFilter.value = val ?? '',
                  decoration: const InputDecoration(labelText: 'Payment'),
                )),
          ],
        ),
        actions: [
          TextButton(
            child: const Text('Clear'),
            onPressed: () {
              _fromDateCtrl.clear();
              _toDateCtrl.clear();
              orderController.paymentStatusFilter.value = '';
              orderController.fetchMyStoreOrders();
              Navigator.pop(ctx);
            },
          ),
          ElevatedButton(
            child: const Text('Apply'),
            onPressed: () {
              orderController.fetchMyStoreOrders(
                fromDate: _fromDateCtrl.text,
                toDate: _toDateCtrl.text,
                paymentStatus: orderController.paymentStatusFilter.value,
              );
              Navigator.pop(ctx);
            },
          ),
        ],
      ),
    );
  }

  /* ───────────── Petit helper date ───────────── */
  Widget _dateField(BuildContext ctx, String label, TextEditingController c) =>
      TextFormField(
        controller: c,
        decoration: InputDecoration(
          labelText: '$label Date',
          suffixIcon: IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () async {
              final d = await showDatePicker(
                context: ctx,
                initialDate: DateTime.now(),
                firstDate: DateTime(2020),
                lastDate: DateTime.now(),
              );
              if (d != null) c.text = d.toString().split(' ').first;
            },
          ),
        ),
        readOnly: true,
      );
}
class StoreSubscriptionPage extends StatelessWidget {
  final AuthController authController = Get.find<AuthController>();

  StoreSubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text(
            "Subscription",
            style: TextStyle(color: AppColors.surface),
          ),
          backgroundColor: AppColors.primary),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Obx(() {
          // Récupérer l'utilisateur de manière sécurisée
          final user = authController.user;
          final hasSubscription = authController.hasSubscription.value;

          if (user == null || !hasSubscription) {
            return Center(
              child: Text(
                "You don't have an active subscription.",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            );
          }

          // Vérifier si les dates existent
          final startDate = user.subscriptionsStartDate;
          final endDate = user.subscriptionsEndDate;

          if (startDate == null || endDate == null) {
            return Center(
              child: Text("Subscription data is missing."),
            );
          }

          return _buildSubscriptionDetails(startDate, endDate);
        }),
      ),
    );
  }

  // Sépare la partie UI de l'affichage des détails de la souscription
  Widget _buildSubscriptionDetails(DateTime startDate, DateTime endDate) {
    final now = DateTime.now();
    final totalDays = endDate.difference(startDate).inDays;
    final remainingDays = endDate.difference(now).inDays;
    final safeRemainingDays = remainingDays > 0 ? remainingDays : 0;
    final progress = safeRemainingDays / totalDays;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                  "Start Date",
                  style:
                      TextStyle(fontSize: 16, fontWeight: AppFontWeights.bold),
                ),
                Text(
                  "${DateFormat.yMMMMd().format(startDate)}",
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
            Column(
              children: [
                Text(
                  "End Date",
                  style:
                      TextStyle(fontSize: 16, fontWeight: AppFontWeights.bold),
                ),
                Text(
                  "${DateFormat.yMMMMd().format(endDate)}",
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 10),
        ClipRRect(
          borderRadius:
              BorderRadius.circular(10), // Applique des bords arrondis
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            color: progress > 0.5 ? AppColors.primary : Colors.red,
            minHeight: 10,
          ),
        ),
        SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              "Time remaining: $safeRemainingDays days",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        SizedBox(height: 20),
        if (safeRemainingDays < 60)
          Center(
            child: Text(
              "Renouvelez votre abonnement dès maintenant pour éviter que votre boutique ne soit plus visible par vos clients.",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }
}

class StoreLocationManagementPage extends StatefulWidget {
  const StoreLocationManagementPage({super.key});

  @override
  State<StoreLocationManagementPage> createState() =>
      _StoreLocationManagementPageState();
}

class _StoreLocationManagementPageState
    extends State<StoreLocationManagementPage> {
  final StoreController storeController = Get.find<StoreController>();

  @override
  void initState() {
    super.initState();
    storeController.fetchMyStoreLocations();
  }

  Future<void> _getCurrentLocation() async {
    try {
      LocationPermission permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) return;

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      storeController
          .updateStorePosition(LatLng(position.latitude, position.longitude));
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  Future<void> _openAddLocation({required bool isEditing}) async {
    await _getCurrentLocation();
    await Get.to(() => AddStoreLocationPage(isEditing: isEditing));
    await storeController.fetchMyStoreLocations();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final location = storeController.myStoreLocations.isNotEmpty
          ? storeController.myStoreLocations.first
          : null;

      return Scaffold(
        appBar: AppBar(
          title: const Text(
            'Localisation du magasin',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
          ),
          centerTitle: true,
          elevation: 0,
          backgroundColor: Colors.white,
        ),
        floatingActionButton: location == null
            ? FloatingActionButton(
                onPressed: () => _openAddLocation(isEditing: false),
                backgroundColor: Colors.black87,
                child: const Icon(Icons.add_location_alt),
              )
            : null,
        body: location == null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.location_off, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'Aucune localisation de magasin définie',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              )
            :  Padding(
    padding: const EdgeInsets.all(8),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Card(
        
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: Colors.grey.shade300),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            
            title: Text(
              location.name ?? 'Nom non défini',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(location.address ?? 'Adresse non définie'),
                const SizedBox(height: 4),
                Text('Lat: ${location.latitude}, Lng: ${location.longitude}'),
              ],
            ),
            trailing: 
            TextButton(
                                  onPressed: () => _openAddLocation(isEditing: true),
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: AppColors.surface,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: Text("edit".tr),
                                ),
          ),
        ),
      ],
    ),
  )

      );
    });
  }
}



class AddStoreLocationPage extends StatelessWidget {
  final StoreController storeController = Get.find<StoreController>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final bool isEditing;

  AddStoreLocationPage({super.key, this.isEditing = false}) {
    if (isEditing && storeController.myStoreLocations.isNotEmpty) {
      final location = storeController.myStoreLocations.first;
      nameController.text = location.name ?? '';
      addressController.text = location.address ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Obx(
            () => GoogleMap(
              initialCameraPosition: CameraPosition(
                target: storeController.currentStorePosition.value,
                zoom: 15,
              ),
              onTap: (LatLng position) {
                storeController.updateStorePosition(position);
              },
              markers: {
                Marker(
                  markerId: const MarkerId('store_location'),
                  position: storeController.currentStorePosition.value,
                  draggable: true,
                  onDragEnd: (LatLng newPosition) {
                    storeController.updateStorePosition(newPosition);
                  },
                ),
              },
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
            ),
          ),
          Positioned(
            top: 40,
            left: 16,
            child: ClipOval(
              child: Container(
                color: AppColors.onBackground,
                child: IconButton(
                  icon: const Icon(FontAwesomeIcons.x,
                      size: 20, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 243, 243, 243),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: InputDecoration(
                      hintText: 'Store Name',
                      suffixIcon: const Icon(Icons.store),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.0)),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: addressController,
                    textInputAction: TextInputAction.done,
                    decoration: InputDecoration(
                      hintText: 'Address',
                      suffixIcon: const Icon(Icons.location_on),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.0)),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                    onPressed: () async {
                      if (nameController.text.isNotEmpty &&
                          addressController.text.isNotEmpty) {
                        storeController.newStoreName.value =
                            nameController.text;
                        storeController.newStoreAddress.value =
                            addressController.text;

                        await storeController.saveMyStoreLocation(
                          name: nameController.text,
                          address: addressController.text,
                          latitude: storeController
                              .currentStorePosition.value.latitude,
                          longitude: storeController
                              .currentStorePosition.value.longitude,
                        );

                        Navigator.of(context).pop();
                      } else {
                        Get.snackbar(
                          'Error',
                          'Please fill in all fields',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.red,
                          colorText: Colors.white,
                        );
                      }
                    },
                    child: Text(isEditing ? 'Update' : 'Save'),
                  ),
                  const SizedBox(height: 30),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
