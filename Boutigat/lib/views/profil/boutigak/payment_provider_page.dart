
import 'package:boutigak/controllers/payment_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/data/services/payment_provider_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PaymentProvidersPage extends StatelessWidget {
  final PaymentController paymentController = Get.put(PaymentController());

  void _showDeleteConfirmation(BuildContext context, StorePaymentProvider provider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Confirmer la suppression'),
          content: Text('Êtes-vous sûr de vouloir supprimer ${provider.providerName} ?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Annuler'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                bool success = await paymentController.deletePaymentProvider(provider.id!);
                if (success) {
                  Get.snackbar(
                    'Succès',
                    'Méthode de paiement supprimée avec succès',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green,
                    colorText: Colors.white,
                  );
                } else {
                  Get.snackbar(
                    'Erreur',
                    'Échec de la suppression',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              },
              child: Text('Supprimer', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    paymentController.fetchPaymentProviders();
    paymentController.fetchStorePaymentProviders();

    return Scaffold(
      appBar: AppBar(
        title: Text('Méthodes de paiement', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.white,
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AddProviderDialog(paymentController: paymentController),
          );
        },
        child: Icon(Icons.add),
        backgroundColor: Colors.black87,
      ),
      body: SafeArea(
        child: Obx(() {
          if (paymentController.isLoading.value) {
            return Center(child: CircularProgressIndicator());
          } else if (paymentController.storePaymentProviders.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Icon(Icons.credit_card_off, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Aucun moyen de paiement ajouté',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Vos méthodes de paiement',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 16),
                  ...paymentController.storePaymentProviders.map((provider) {
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.05),
                            blurRadius: 6,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),

                            child: Image.network(
                              provider.providerLogo ?? '',
                              width: 50,
                              height: 50,
                              errorBuilder: (context, error, stackTrace) =>
                                  Icon(Icons.payment, size: 40, color: Colors.grey),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  provider.providerName ,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (provider.phoneNumber != null)
                                  Text('Téléphone: ${provider.phoneNumber}',
                                      style: TextStyle(color: Colors.grey[700])),
                                if (provider.paymentCode != null)
                                  Text('Code: ${provider.paymentCode}',
                                      style: TextStyle(color: Colors.grey[700])),
                              ],
                            ),
                          ),
                          // Action buttons
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => EditProviderDialog(
                                      paymentController: paymentController,
                                      provider: provider,
                                    ),
                                  );
                                },
                                icon: Icon(Icons.edit, color: Colors.blue),
                                tooltip: 'Modifier',
                              ),
                              IconButton(
                                onPressed: () {
                                  _showDeleteConfirmation(context, provider);
                                },
                                icon: Icon(Icons.delete, color: Colors.red),
                                tooltip: 'Supprimer',
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}

class PaymentForm extends StatelessWidget {
  final PaymentController paymentController;
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController paymentCodeController = TextEditingController();

  PaymentForm({required this.paymentController});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() => DropdownButtonFormField<String>(
                value: paymentController.selectedProvider.value.isEmpty
                    ? null
                    : paymentController.selectedProvider.value,
                hint: Text('Sélectionnez une méthode'),
                onChanged: (newValue) {
                  if (newValue != null) {
                    paymentController.selectedProvider.value = newValue;
                  }
                },
                items: paymentController.paymentProviders.map((provider) {
                  return DropdownMenuItem<String>(
                    value: provider.id.toString(),
                    child: Row(
                      children: [
                        if (provider.logoUrl != null && provider.logoUrl!.isNotEmpty)
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: Image.network(provider.logoUrl!,
                                width: 24, height: 24, fit: BoxFit.cover),
                          ),
                        SizedBox(width: 8),
                        Text(provider.name),
                      ],
                    ),
                  );
                }).toList(),
                decoration: InputDecoration(
                 
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              )),
          SizedBox(height: 16),
          TextFormField(
            controller: phoneController,
            decoration: InputDecoration(
              hintText: 'Numéro de téléphone',
              
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            ),
            keyboardType: TextInputType.phone,
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: paymentCodeController,
            decoration: InputDecoration(
              hintText: 'Code de paiement',
              
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton(
            onPressed: () async {
              if (paymentController.selectedProvider.value.isEmpty ||
                  (phoneController.text.isEmpty && paymentCodeController.text.isEmpty)) {
                Get.snackbar(
                  'Erreur',
                  'Veuillez remplir tous les champs requis',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              Map<String, dynamic> requestBody = {
                'provider_id': int.parse(paymentController.selectedProvider.value),
                'payment_code': paymentCodeController.text,
                'phone_number': phoneController.text,
              };

              bool success = await PaymentService.addPaymentProvider(requestBody);

              if (success) {
                await paymentController.fetchStorePaymentProviders();
                Get.snackbar('Succès', 'Méthode ajoutée avec succès',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green,
                    colorText: Colors.white);
                Get.back();
              } else {
                Get.snackbar('Erreur', 'Échec de l’ajout', snackPosition: SnackPosition.BOTTOM);
              }
            },
            child: Text('Enregistrer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black87,
              padding: EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
        ],
      ),
    );
  }
}
class AddProviderDialog extends StatelessWidget {
  final PaymentController paymentController;

  AddProviderDialog({required this.paymentController});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: PaymentForm(paymentController: paymentController),
      ),
    );
  }
}

class EditProviderDialog extends StatelessWidget {
  final PaymentController paymentController;
  final StorePaymentProvider provider;

  EditProviderDialog({required this.paymentController, required this.provider});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: EditPaymentForm(
          paymentController: paymentController,
          provider: provider,
        ),
      ),
    );
  }
}

class EditPaymentForm extends StatelessWidget {
  final PaymentController paymentController;
  final StorePaymentProvider provider;
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController paymentCodeController = TextEditingController();

  EditPaymentForm({required this.paymentController, required this.provider}) {
    // Initialize controllers with existing values
    phoneController.text = provider.phoneNumber ?? '';
    paymentCodeController.text = provider.paymentCode ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Modifier ${provider.providerName}',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 20),
          // Display provider info (read-only)
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Image.network(
                    provider.providerLogo ?? '',
                    width: 40,
                    height: 40,
                    errorBuilder: (context, error, stackTrace) =>
                        Icon(Icons.payment, size: 30, color: Colors.grey),
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  provider.providerName,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: phoneController,
            decoration: InputDecoration(
              labelText: 'Numéro de téléphone',
              hintText: 'Numéro de téléphone',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            ),
            keyboardType: TextInputType.phone,
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: paymentCodeController,
            decoration: InputDecoration(
              labelText: 'Code de paiement',
              hintText: 'Code de paiement',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            ),
          ),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => Get.back(),
                  child: Text('Annuler'),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () async {
                    if (phoneController.text.isEmpty && paymentCodeController.text.isEmpty) {
                      Get.snackbar(
                        'Erreur',
                        'Veuillez remplir au moins un champ',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                      return;
                    }

                    bool success = await paymentController.updatePaymentProvider(
                      provider.id!,
                      paymentCodeController.text,
                      phoneController.text,
                    );

                    if (success) {
                      Get.snackbar(
                        'Succès',
                        'Méthode mise à jour avec succès',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.green,
                        colorText: Colors.white,
                      );
                      Get.back();
                    } else {
                      Get.snackbar(
                        'Erreur',
                        'Échec de la mise à jour',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                    }
                  },
                  child: Text('Mettre à jour'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black87,
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

