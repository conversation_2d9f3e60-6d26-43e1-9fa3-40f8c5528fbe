import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';


class PaymentService {
   static Future<List<PaymentProvider>?> fetchPaymentProviders() async {
    var response = await WebService.get(AvailableServices.paymentProviders);

    if (response.statusCode == 200) {
      // Decode the response body
      Map<String, dynamic> responseBody = jsonDecode(response.body);


      List<dynamic> data = responseBody['data'];


      log('paymenbt providers ${data}');
      List<PaymentProvider> paymentProviders = data.map((dynamic item) => PaymentProvider.fromJson(item)).toList();
      return paymentProviders;
    } else {
      // Get.snakbar("Error", "Failed to load payment providers");
      return null;
    }
  }



static Future<List<StorePaymentProvider>?> fetchStorePaymentProviders() async {
    try {
      var response = await WebService.get(AvailableServices.getpaymentProviders);




  print('Response  StorePaymentProvider code: ${response.body}');
       print('response body ++=++++++++++ ${response.body}+++++++++++++');
 
      if (response.statusCode == 200) {
        // Decode the response body
        List<dynamic> data = jsonDecode(response.body);

        // Map the JSON to a list of StorePaymentProvider objects
        List<StorePaymentProvider> storePaymentProviders = data.map((dynamic item) => StorePaymentProvider.fromJson(item)).toList();
        return storePaymentProviders;
      } else {
        // Get.snakbar("Error", "Failed to load store payment providers");
        return null;
      }
    } catch (e) {
      print("Error fetching store payment providers: $e");
      return null;
    }
  }


static Future<List<StorePaymentProvider>?> fetchStoreProvidersById(int storeId) async {
  try {
    var response = await WebService.get('${AvailableServices.storePaymentProviders}/$storeId/payment-providers');




    print('response payment providers ${response.body}');
    if (response.statusCode == 200) {
      List<dynamic> data = jsonDecode(response.body);
      List<StorePaymentProvider> storePaymentProviders = data
          .map((dynamic item) => StorePaymentProvider.fromJson(item))
          .toList();
      return storePaymentProviders;
    } else {
      return null;
    }
  } catch (e) {
    print("Error fetching store payment providers: $e");
    return null;
  }
}


 // Method to add a new payment provider
  static Future<bool> addPaymentProvider(Map<String, dynamic> requestBody) async {
    try {
      var response = await WebService.post(
        AvailableServices.addpaymentProviders,
        body: requestBody,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        print('Failed to add payment provider: ${response.body}');
        return false;
      }
    } catch (e) {
      print("Error adding payment provider: $e");
      return false;
    }
  }

  // Method to update a payment provider
  static Future<bool> updatePaymentProvider(int id, Map<String, dynamic> requestBody) async {
    try {
      var response = await WebService.put(
        '${AvailableServices.updatepaymentProvider}/$id',
        body: requestBody,
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        print('Failed to update payment provider: ${response.body}');
        return false;
      }
    } catch (e) {
      print("Error updating payment provider: $e");
      return false;
    }
  }

  // Method to delete a payment provider
  static Future<bool> deletePaymentProvider(int id) async {
    try {
      var response = await WebService.delete(
        '${AvailableServices.deletepaymentProvider}/$id',
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        print('Failed to delete payment provider: ${response.body}');
        return false;
      }
    } catch (e) {
      print("Error deleting payment provider: $e");
      return false;
    }
  }


  // static Future<bool> sendPayment(Payment payment) async {
  //   try {
  //     // Préparation des champs pour la requête multipart
  //     Map<String, String> fields = {
  //       'to_store': payment.toStore.toString(),
  //       'provider_id': payment.providerId.toString(),
  //       if (payment.itemId != null) 'item_id': payment.itemId.toString(),
  //       if (payment.orderId != null) 'order_id': payment.orderId.toString(),
  //       if (payment.promoCode != null) 'promo_code': payment.promoCode!,
  //     };

  //     // Préparation du fichier screenshot
  //     List<File> files = [
  //       File(payment.screenshot), // Assuming `screenshot` contains the file path
  //     ];

  //     // Envoi de la requête multipart via une fonction utilitaire
  //     var response = await WebService.postMultipart(
  //       AvailableServices.paymentProof,
  //       fields: fields,
  //       files: files,
  //     );

  //     // Gestion de la réponse
  //     var responseString = await response.stream.bytesToString();
  //     var responseJson = jsonDecode(responseString);

  //     if (response.statusCode == 201) {
  //       // Get.snakbar("Succès", "Paiement envoyé avec succès");
  //       return true;
  //     } else {
  //       // Get.snakbar("Erreur", "Échec de l'envoi du paiement. Statut : ${response.statusCode}");
  //       return false;
  //     }
  //   } catch (e) {
  //     print('Erreur lors de l\'envoi du paiement: $e');
  //     // Get.snakbar("Erreur", "Une erreur s'est produite lors de l'envoi du paiement : ${e.toString()}");
  //     return false;
  //   }
  // }



  static Future<Map<String, dynamic>> processBPayPayment({
    required String phone,
    required String passcode,
    required double amount,
    required String itemId,
  }) async {
    try {



      print('BPay process request: $phone, $passcode, $amount, $itemId');

      var response = await WebService.post(
        AvailableServices.bpayProcess,
        body: {
          'phone': phone,
          'passcode': passcode,
          'amount': amount.toString(),
          'item_id': itemId
        },
      );
 
      print('BPay process response: ${response.body}');
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return {
            'success': true,
            'data': responseData['data'],
          };
        } else {
          return {
            'success': false,
            'message': responseData['message'] ?? 'Payment processing failed',
          };
        }
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'message': errorData['message'] ?? 'Payment processing failed',
        };
      }
    } catch (e) {
      print("Error processing BPay payment: $e");
      return {
        'success': false,
        'message': 'An error occurred while processing the payment',
      };
    }
  }

  static Future<Map<String, dynamic>> verifyPromoCode({
    required String promoCode,
    required int itemId,
    required double originalAmount,
  }) async {
    try {
      var response = await WebService.post(
        '${AvailableServices.promoCodeVerify}',
        body: {
          'promo_code': promoCode,
          'item_id': itemId,
          'original_amount': originalAmount,
        },
      );


      print('promo code response: ${response.body}');
      print('promo code response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return {
          'success': false,
          'message': 'Failed to verify promo code',
          'amount': originalAmount,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Error verifying promo code',
        'amount': originalAmount,
      };
    }
  }
}
