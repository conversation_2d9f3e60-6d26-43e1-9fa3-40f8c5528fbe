<?php

namespace App\Http\Controllers;

use App\Models\Store;
use App\Models\StorePaymentProvider;
use App\Models\EPaymentProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class StorePaymentProviderController extends Controller
{

     /**
     * @OA\Post(
     *     path="/api/stores/payment-providers/add",
     *     tags={"Store Payment Providers"},
     *     summary="Add a new payment provider to store",
     *     description="Add a new payment provider with payment details to the authenticated user's store",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"provider_id"},
     *             @OA\Property(
     *                 property="provider_id",
     *                 type="integer",
     *                 description="ID of the payment provider"
     *             ),
     *             @OA\Property(
     *                 property="payment_code",
     *                 type="string",
     *                 description="Payment code for the provider",
     *                 nullable=true
     *             ),
     *             @OA\Property(
     *                 property="phone_number",
     *                 type="string",
     *                 description="Phone number for the provider",
     *                 nullable=true
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Provider added successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="Payment provider added successfully"
     *             ),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="provider_id", type="integer"),
     *                 @OA\Property(property="provider_name", type="string"),
     *                 @OA\Property(property="provider_logo", type="string"),
     *                 @OA\Property(property="payment_code", type="string"),
     *                 @OA\Property(property="phone_number", type="string"),
     *                 @OA\Property(property="is_active", type="boolean")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Provider already exists for this store",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="This payment provider is already added to your store"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="Store not found"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="errors",
     *                 type="object"
     *             )
     *         )
     *     )
     * )
     */
    public function addProvider(Request $request)
    {
        $store = Auth::user()->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:e_payment_providers,id',
            'payment_code' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if provider already exists for this store
        $existingProvider = StorePaymentProvider::where('store_id', $store->id)
            ->where('provider_id', $request->provider_id)
            ->first();

        if ($existingProvider) {
            return response()->json([
                'message' => 'This payment provider is already added to your store'
            ], 400);
        }

        // Create new store payment provider
        $storeProvider = StorePaymentProvider::create([
            'store_id' => $store->id,
            'provider_id' => $request->provider_id,
            'payment_code' => $request->payment_code ?? null,
            'phone_number' => $request->phone_number ?? null,
        ]);

        // Load provider relationship for response
        $storeProvider->load('provider');

        // Format response
        $response = [
            'id' => $storeProvider->id,
            'provider_id' => $storeProvider->provider_id,
            'provider_name' => $storeProvider->provider->name,
            'provider_logo' => $storeProvider->provider->logo_url,
            'payment_code' => $storeProvider->payment_code,
            'phone_number' => $storeProvider->phone_number,
        ];

        return response()->json([
            'message' => 'Payment provider added successfully',
            'data' => $response
        ], 201);
    }
    /**
     * @OA\Get(
     *     path="/api/stores/payment-providers",
     *     tags={"Store Payment Providers"},
     *     summary="Get store payment providers",
     *     description="Get all payment providers for the authenticated user's store with their payment codes and phone numbers",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="provider_id", type="integer"),
     *                 @OA\Property(property="provider_name", type="string"),
     *                 @OA\Property(property="provider_logo", type="string"),
     *                 @OA\Property(property="payment_code", type="string"),
     *                 @OA\Property(property="phone_number", type="string"),
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function getStoreProviders()
    {
        $store = Auth::user()->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $providers = StorePaymentProvider::with('provider')
            ->where('store_id', $store->id)
            ->get()
            ->map(function ($storeProvider) {
                return [
                    'id' => $storeProvider->id,
                    'provider_id' => $storeProvider->provider_id,
                    'provider_name' => $storeProvider->provider->name,
                    'provider_logo' => $storeProvider->provider->logo_url,
                    'payment_code' => $storeProvider->payment_code,
                    'phone_number' => $storeProvider->phone_number,
                    'logo' =>  $storeProvider->provider->logo,
                ];
            });

        return response()->json($providers);
    }

    /**
     * @OA\Post(
     *     path="/api/stores/payment-providers",
     *     tags={"Store Payment Providers"},
     *     summary="Update store payment providers",
     *     description="Update payment providers information for the store in bulk",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="provider_id", type="integer"),
     *                 @OA\Property(property="payment_code", type="string", nullable=true),
     *                 @OA\Property(property="phone_number", type="string", nullable=true),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Providers updated successfully"
     *     ),
     *     @OA\Response(response=404, description="Store not found"),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function updateStoreProviders(Request $request)
    {
        $store = Auth::user()->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            '*.provider_id' => 'required|exists:e_payment_providers,id',
            '*.payment_code' => 'nullable|string',
            '*.phone_number' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        foreach ($request->all() as $providerData) {
            StorePaymentProvider::updateOrCreate(
                [
                    'store_id' => $store->id,
                    'provider_id' => $providerData['provider_id']
                ],
                [
                    'payment_code' => $providerData['payment_code'] ?? null,
                    'phone_number' => $providerData['phone_number'] ?? null,
                ]
            );
        }

        return response()->json(['message' => 'Providers updated successfully']);
    }

    /**
     * @OA\Get(
     *     path="/api/stores/{store_id}/payment-providers/{provider_id}",
     *     tags={"Store Payment Providers"},
     *     summary="Get specific provider details",
     *     description="Get payment code and phone number for a specific provider of a store",
     *     @OA\Parameter(
     *         name="store_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="provider_id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="payment_code", type="string"),
     *             @OA\Property(property="phone_number", type="string")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Provider not found for this store")
     * )
     */
    public function getStoreProviderDetails($storeId, $providerId)
    {
        $storeProvider = StorePaymentProvider::where('store_id', $storeId)
            ->where('provider_id', $providerId)
            ->where('is_active', true)
            ->first();

        if (!$storeProvider) {
            return response()->json(['message' => 'Provider not found for this store'], 404);
        }

        return response()->json([
            'payment_code' => $storeProvider->payment_code,
            'phone_number' => $storeProvider->phone_number
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/stores/{storeId}/payment-providers",
     *     tags={"Store Payment Providers"},
     *     summary="Get store payment providers by store ID",
     *     @OA\Parameter(
     *         name="storeId",
     *         in="path",
     *         required=true,
     *         description="ID of the store",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="List of store payment providers"),
     *     @OA\Response(response=404, description="Store not found")
     * )
     */
    public function getStoreProvidersById($storeId)
    {
        $providers = StorePaymentProvider::with('provider')
            ->where('store_id', $storeId)
            ->where('is_active', true)
            ->get()
            ->map(function ($storeProvider) {
                return [
                    'id' => $storeProvider->id,
                    'provider_id' => $storeProvider->provider_id,
                    'providerName' => $storeProvider->provider->name,
                    'providerLogo' => $storeProvider->provider->logo_url,
                    'payment_code' => $storeProvider->payment_code,
                    'phone_number' => $storeProvider->phone_number,
                    'is_active' => $storeProvider->is_active,
                    'logo' => $storeProvider->provider->logo,
                ];
            });

        return response()->json($providers);
    }

    /**
     * @OA\Put(
     *     path="/api/stores/payment-providers/{id}",
     *     tags={"Store Payment Providers"},
     *     summary="Update a specific payment provider",
     *     description="Update payment details for a specific store payment provider",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the store payment provider",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="payment_code",
     *                 type="string",
     *                 description="Payment code for the provider",
     *                 nullable=true
     *             ),
     *             @OA\Property(
     *                 property="phone_number",
     *                 type="string",
     *                 description="Phone number for the provider",
     *                 nullable=true
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Provider updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="Payment provider updated successfully"
     *             ),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="provider_id", type="integer"),
     *                 @OA\Property(property="provider_name", type="string"),
     *                 @OA\Property(property="provider_logo", type="string"),
     *                 @OA\Property(property="payment_code", type="string"),
     *                 @OA\Property(property="phone_number", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Payment provider not found",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="Payment provider not found"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized to update this provider",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="You are not authorized to update this payment provider"
     *             )
     *         )
     *     )
     * )
     */
    public function updateProvider(Request $request, $id)
    {
        $store = Auth::user()->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'payment_code' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Find the store payment provider
        $storeProvider = StorePaymentProvider::where('id', $id)
            ->where('store_id', $store->id)
            ->first();

        if (!$storeProvider) {
            return response()->json([
                'message' => 'Payment provider not found or you are not authorized to update it'
            ], 404);
        }

        // Update the provider
        $storeProvider->update([
            'payment_code' => $request->payment_code ?? $storeProvider->payment_code,
            'phone_number' => $request->phone_number ?? $storeProvider->phone_number,
        ]);

        // Load provider relationship for response
        $storeProvider->load('provider');

        // Format response
        $response = [
            'id' => $storeProvider->id,
            'provider_id' => $storeProvider->provider_id,
            'provider_name' => $storeProvider->provider->name,
            'provider_logo' => $storeProvider->provider->logo_url,
            'payment_code' => $storeProvider->payment_code,
            'phone_number' => $storeProvider->phone_number,
        ];

        return response()->json([
            'message' => 'Payment provider updated successfully',
            'data' => $response
        ], 200);
    }

    /**
     * @OA\Delete(
     *     path="/api/stores/payment-providers/{id}",
     *     tags={"Store Payment Providers"},
     *     summary="Delete a payment provider",
     *     description="Delete a specific payment provider from the store",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID of the store payment provider",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Provider deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="Payment provider deleted successfully"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Payment provider not found",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="Payment provider not found"
     *             )
     *         )
     *     )
     * )
     */
    public function deleteProvider($id)
    {
        $store = Auth::user()->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        // Find the store payment provider
        $storeProvider = StorePaymentProvider::where('id', $id)
            ->where('store_id', $store->id)
            ->first();

        if (!$storeProvider) {
            return response()->json([
                'message' => 'Payment provider not found or you are not authorized to delete it'
            ], 404);
        }

        // Delete the provider
        $storeProvider->delete();

        return response()->json([
            'message' => 'Payment provider deleted successfully'
        ], 200);
    }
}
